/**knowledge-detail.scss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
}

/* 文章头部 */
.article-header {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 30rpx 0 20rpx 0;
}

.article-banner {
  width: 100%;
  height: 400rpx;
}

.header-content {
  padding: 30rpx;
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: block;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.meta-left {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.publish-time {
  font-size: 22rpx;
  color: #999;
}

.meta-right {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  width: 24rpx;
  height: 24rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #999;
}

.article-tags {
  display: flex;
  gap: 12rpx;
}

.tag {
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

.category-tag {
  background: #E3F2FD;
  color: #1976D2;
}

.age-tag {
  background: #F3E5F5;
  color: #7B1FA2;
}

/* 文章内容 */
.article-content {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  line-height: 1.8;
}

/* 相关推荐 */
.related-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.related-item {
  display: flex;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.related-thumb {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.related-content {
  flex: 1;
}

.related-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-summary {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
}

.related-meta {
  display: flex;
  gap: 16rpx;
}

.related-author,
.related-time {
  font-size: 20rpx;
  color: #999;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 0;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
}

.action-icon.liked {
  filter: hue-rotate(320deg) saturate(2);
}

.action-icon.favorited {
  filter: hue-rotate(40deg) saturate(2);
}

.action-text {
  font-size: 22rpx;
  color: #666;
}

.comment-btn {
  background: #4A90E2;
  border-radius: 12rpx;
  margin: 0 20rpx;
}

.comment-btn .action-text {
  color: white;
}

.comment-btn .action-icon {
  filter: brightness(0) invert(1);
}
