/**index.scss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
}

/* 头部横幅 */
.header-banner {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin: 30rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.banner-content {
  flex: 1;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.banner-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  display: block;
}

.banner-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-emoji {
    font-size: 60rpx;
  }
}

/* 主要功能入口 */
.main-functions {
  margin: 40rpx 0;
}

.function-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
}

.calculator-icon {
  background: linear-gradient(135deg, #FFB74D 0%, #FF9800 100%);
}

.knowledge-icon {
  background: linear-gradient(135deg, #81C784 0%, #4CAF50 100%);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.card-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 通用区块样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0 20rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #4A90E2;
}

/* 热门知识推荐 */
.knowledge-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.knowledge-item {
  display: flex;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.knowledge-item:last-child {
  border-bottom: none;
}

.knowledge-thumb {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.knowledge-info {
  flex: 1;
}

.knowledge-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.knowledge-summary {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.knowledge-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.knowledge-tag {
  background: #E3F2FD;
  color: #1976D2;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.knowledge-time {
  font-size: 20rpx;
  color: #999;
}

/* 政策资讯 */
.news-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
}

.news-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.news-item:last-child {
  border-bottom: none;
}

.news-content {
  flex: 1;
  margin-right: 20rpx;
}

.news-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.news-summary {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-time {
  font-size: 20rpx;
  color: #999;
}

.news-tag {
  background: #F5F5F5;
  color: #666;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

.new-tag {
  background: #FF5722;
  color: white;
}
