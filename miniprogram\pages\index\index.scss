/**index.scss**/
@import '../../styles/design-system.scss';

page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
}

/* 头部横幅 */
.header-banner {
  background: var(--primary-gradient);
  border-radius: var(--rounded-xl);
  padding: 40rpx 30rpx;
  margin: 30rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-primary);
}

.header-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.banner-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.banner-title {
  font-size: 36rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 10rpx;
  letter-spacing: 1rpx;
}

.banner-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  display: block;
  font-weight: 400;
}

.banner-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10rpx);

  .icon-emoji {
    font-size: 60rpx;
  }
}

/* 主要功能入口 */
.main-functions {
  margin: 40rpx 0;
}

.function-card {
  background: var(--bg-primary);
  border-radius: var(--rounded-lg);
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-medium);
  transition: all 0.2s ease;
  border: 1rpx solid transparent;
}

.function-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-light);
  border-color: var(--primary-color);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
}

.calculator-icon {
  background: linear-gradient(135deg, #FFB74D 0%, #FF9800 100%);
}

.knowledge-icon {
  background: linear-gradient(135deg, #81C784 0%, #4CAF50 100%);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  display: block;
}

.card-arrow {
  font-size: 32rpx;
  color: var(--text-tertiary);
  transition: color 0.2s ease;
}

.function-card:active .card-arrow {
  color: var(--primary-color);
}

/* 通用区块样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0 20rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #4A90E2;
}

/* 热门知识推荐 */
.knowledge-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.knowledge-item {
  display: flex;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.knowledge-item:active {
  background-color: #f8f8f8;
}

.knowledge-item:last-child {
  border-bottom: none;
}

.knowledge-thumb {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.knowledge-info {
  flex: 1;
}

.knowledge-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.knowledge-summary {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.knowledge-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.knowledge-tag {
  background: #E3F2FD;
  color: #1976D2;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.knowledge-time {
  font-size: 20rpx;
  color: #999;
}

/* 政策资讯 */
.news-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
}

.news-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.news-item:active {
  background-color: #f8f8f8;
}

.news-item:last-child {
  border-bottom: none;
}

.news-content {
  flex: 1;
  margin-right: 20rpx;
}

.news-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.news-summary {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-time {
  font-size: 20rpx;
  color: #999;
}

.news-tag {
  background: #F5F5F5;
  color: #666;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

.new-tag {
  background: #FF5722;
  color: white;
}
