# 图标资源指南

## 底部导航栏图标

微信小程序的底部导航栏（tabBar）需要PNG格式的图标，建议尺寸为64x64像素。

### 所需图标列表

1. **首页图标**
   - 未选中：tab-home.png
   - 选中：tab-home-active.png
   - 图案：房子/主页图标

2. **补贴计算图标**
   - 未选中：tab-calculator.png
   - 选中：tab-calculator-active.png
   - 图案：计算器图标

3. **育儿知识图标**
   - 未选中：tab-knowledge.png
   - 选中：tab-knowledge-active.png
   - 图案：书本/知识图标

4. **个人中心图标**
   - 未选中：tab-profile.png
   - 选中：tab-profile-active.png
   - 图案：用户/个人图标

### 设计规范

- **尺寸**：64x64像素（推荐）
- **格式**：PNG
- **颜色**：
  - 未选中状态：#666666（灰色）
  - 选中状态：#4A90E2（蓝色）
- **背景**：透明
- **线条粗细**：2-3像素
- **风格**：简洁、线性图标

### 获取图标的方法

#### 方法1：在线图标库
1. **Iconfont（阿里巴巴矢量图标库）**
   - 网址：https://www.iconfont.cn/
   - 搜索关键词：home, calculator, book, user
   - 下载PNG格式，64x64尺寸

2. **Icons8**
   - 网址：https://icons8.com/
   - 提供免费图标下载
   - 支持自定义颜色和尺寸

3. **Flaticon**
   - 网址：https://www.flaticon.com/
   - 大量免费图标资源
   - 支持PNG和SVG格式

#### 方法2：设计工具制作
1. **Figma**
   - 使用矢量工具绘制图标
   - 导出为PNG格式

2. **Adobe Illustrator**
   - 创建矢量图标
   - 导出为PNG

3. **Sketch**
   - 设计图标
   - 导出为PNG

#### 方法3：SVG转PNG
如果有SVG格式的图标，可以使用以下工具转换：
1. **在线转换工具**
   - https://convertio.co/svg-png/
   - https://cloudconvert.com/svg-to-png

2. **命令行工具**
   ```bash
   # 使用ImageMagick
   convert icon.svg -resize 64x64 icon.png
   ```

### 配置图标

获取图标后，将PNG文件放入 `miniprogram/images/` 目录，然后在 `app.json` 中配置：

```json
{
  "tabBar": {
    "color": "#666666",
    "selectedColor": "#4A90E2",
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "images/tab-home.png",
        "selectedIconPath": "images/tab-home-active.png"
      },
      {
        "pagePath": "pages/calculator/calculator",
        "text": "补贴计算",
        "iconPath": "images/tab-calculator.png",
        "selectedIconPath": "images/tab-calculator-active.png"
      },
      {
        "pagePath": "pages/knowledge/knowledge",
        "text": "育儿知识",
        "iconPath": "images/tab-knowledge.png",
        "selectedIconPath": "images/tab-knowledge-active.png"
      },
      {
        "pagePath": "pages/profile/profile",
        "text": "我的",
        "iconPath": "images/tab-profile.png",
        "selectedIconPath": "images/tab-profile-active.png"
      }
    ]
  }
}
```

### 注意事项

1. **文件大小**：每个图标文件建议小于40KB
2. **命名规范**：使用小写字母和连字符
3. **版权问题**：确保使用的图标有合法授权
4. **测试**：在不同设备上测试图标显示效果
5. **备用方案**：如果暂时没有图标，可以先使用纯文字导航

### 临时解决方案

当前配置中已移除图标路径，使用纯文字导航。这样可以：
1. 避免编译错误
2. 正常运行小程序
3. 后续添加图标时再配置路径

等获取到合适的PNG图标后，再按照上述配置方法添加到项目中。
