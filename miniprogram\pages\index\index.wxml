<!--index.wxml-->
<page-meta>
  <navigation-bar title="育儿补贴助手" back="{{false}}" color="white" background="#4A90E2"></navigation-bar>
</page-meta>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 头部横幅 -->
    <view class="header-banner">
      <view class="banner-content">
        <text class="banner-title">智能计算育儿补贴</text>
        <text class="banner-subtitle">让每一份补贴都不错过</text>
      </view>
      <view class="banner-icon">
        <text class="icon-emoji">👶</text>
      </view>
    </view>

    <!-- 主要功能入口 -->
    <view class="main-functions">
      <view class="function-card" bindtap="goToCalculator">
        <view class="card-icon calculator-icon">💰</view>
        <view class="card-content">
          <text class="card-title">补贴计算</text>
          <text class="card-desc">快速计算各类育儿补贴</text>
        </view>
        <view class="card-arrow">></view>
      </view>

      <view class="function-card" bindtap="goToKnowledge">
        <view class="card-icon knowledge-icon">📚</view>
        <view class="card-content">
          <text class="card-title">育儿知识</text>
          <text class="card-desc">专业育儿指导与建议</text>
        </view>
        <view class="card-arrow">></view>
      </view>
    </view>

    <!-- 热门知识推荐 -->
    <view class="hot-knowledge">
      <view class="section-header">
        <text class="section-title">热门知识</text>
        <text class="section-more" bindtap="goToKnowledge">更多 ></text>
      </view>
      <view class="knowledge-list">
        <view class="knowledge-item" wx:for="{{hotKnowledge}}" wx:key="id" bindtap="goToKnowledgeDetail" data-id="{{item.id}}">
          <image class="knowledge-thumb" src="{{item.thumbnail}}" mode="aspectFill"></image>
          <view class="knowledge-info">
            <text class="knowledge-title">{{item.title}}</text>
            <text class="knowledge-summary">{{item.summary}}</text>
            <view class="knowledge-meta">
              <text class="knowledge-tag">{{item.category}}</text>
              <text class="knowledge-time">{{item.publishTime}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 最新政策资讯 -->
    <view class="policy-news">
      <view class="section-header">
        <text class="section-title">政策资讯</text>
        <text class="section-more" bindtap="goToPolicyNews">更多 ></text>
      </view>
      <view class="news-list">
        <view class="news-item" wx:for="{{policyNews}}" wx:key="id" bindtap="goToNewsDetail" data-id="{{item.id}}">
          <view class="news-content">
            <text class="news-title">{{item.title}}</text>
            <text class="news-summary">{{item.summary}}</text>
            <text class="news-time">{{item.publishTime}}</text>
          </view>
          <view class="news-tag {{item.isNew ? 'new-tag' : ''}}">
            {{item.isNew ? 'NEW' : item.region}}
          </view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
