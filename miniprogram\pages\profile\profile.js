// profile.js
Component({
  data: {
    userInfo: {
      isLogin: false,
      nickName: '',
      avatarUrl: '',
      desc: '',
      isVip: false
    },
    userStats: {
      calculationCount: 0,
      favoriteCount: 0,
      readingCount: 0
    }
  },

  methods: {
    // 用户登录
    login() {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          this.setData({
            'userInfo.isLogin': true,
            'userInfo.nickName': res.userInfo.nickName,
            'userInfo.avatarUrl': res.userInfo.avatarUrl,
            'userInfo.desc': '已登录，享受个性化服务'
          })
          this.loadUserStats()
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })
        },
        fail: () => {
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          })
        }
      })
    },

    // 选择头像
    chooseAvatar() {
      if (!this.data.userInfo.isLogin) {
        this.login()
        return
      }

      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.setData({
            'userInfo.avatarUrl': res.tempFiles[0].tempFilePath
          })
          wx.showToast({
            title: '头像更新成功',
            icon: 'success'
          })
        }
      })
    },

    // 加载用户统计数据
    loadUserStats() {
      this.setData({
        userStats: {
          calculationCount: Math.floor(Math.random() * 50) + 1,
          favoriteCount: Math.floor(Math.random() * 20) + 1,
          readingCount: Math.floor(Math.random() * 100) + 1
        }
      })
    },

    // 跳转到计算历史
    goToCalculationHistory() {
      if (!this.checkLogin()) return
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 跳转到收藏页面
    goToFavorites() {
      if (!this.checkLogin()) return
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 跳转到阅读记录
    goToReadingHistory() {
      if (!this.checkLogin()) return
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 跳转到设置页面
    goToSettings() {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 跳转到意见反馈
    goToFeedback() {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 跳转到关于我们
    goToAbout() {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 分享应用
    shareApp() {
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
      wx.showToast({
        title: '请点击右上角分享',
        icon: 'none'
      })
    },

    // 清理缓存
    clearCache() {
      wx.showModal({
        title: '清理缓存',
        content: '确定要清理本地缓存吗？',
        success: (res) => {
          if (res.confirm) {
            wx.clearStorageSync()
            wx.showToast({
              title: '缓存清理完成',
              icon: 'success'
            })
          }
        }
      })
    },

    // 导出数据
    exportData() {
      if (!this.checkLogin()) return
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 退出登录
    logout() {
      wx.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              userInfo: {
                isLogin: false,
                nickName: '',
                avatarUrl: '',
                desc: '',
                isVip: false
              },
              userStats: {
                calculationCount: 0,
                favoriteCount: 0,
                readingCount: 0
              }
            })
            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            })
          }
        }
      })
    },

    // 检查登录状态
    checkLogin() {
      if (!this.data.userInfo.isLogin) {
        wx.showModal({
          title: '提示',
          content: '请先登录',
          showCancel: false,
          success: () => {
            this.login()
          }
        })
        return false
      }
      return true
    }
  },

  // 页面生命周期
  lifetimes: {
    attached() {
      const userInfo = wx.getStorageSync('userInfo')
      if (userInfo) {
        this.setData({ userInfo })
        this.loadUserStats()
      }
    }
  },

  // 页面事件
  pageLifetimes: {
    show() {
      if (this.data.userInfo.isLogin) {
        this.loadUserStats()
      }
    }
  }
})