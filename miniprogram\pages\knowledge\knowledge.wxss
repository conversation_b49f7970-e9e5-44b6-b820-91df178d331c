/**knowledge.wxss**/
@import '../../../styles/design-system.scss';

page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
}

/* 搜索栏 */
.search-section {
  padding: 30rpx 0 20rpx;
  position: sticky;
  top: 0;
  background: var(--bg-secondary);
  z-index: 10;
}

.search-bar {
  display: flex;
  align-items: center;
  background: var(--bg-primary);
  border-radius: var(--rounded-xl);
  padding: 20rpx 24rpx;
  box-shadow: var(--shadow-light);
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.search-bar:focus-within {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-medium);
}

.search-icon {
  font-size: 32rpx;
  color: var(--text-tertiary);
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-primary);
  background: transparent;
  border: none;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

/* 分类标签 */
.category-section {
  margin: 20rpx 0;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 10rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 16rpx 24rpx;
  background: var(--bg-primary);
  border-radius: var(--rounded-xl);
  font-size: 24rpx;
  color: var(--text-secondary);
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-light);
}

.category-item.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.category-item:active {
  transform: scale(0.95);
}

/* 文章列表 */
.article-list {
  margin: 20rpx 0;
}

.article-card {
  background: var(--bg-primary);
  border-radius: var(--rounded-lg);
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  transition: all 0.2s ease;
}

.article-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-light);
}

.article-image {
  width: 100%;
  height: 300rpx;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: 24rpx;
}

.article-content {
  padding: 30rpx;
}

.article-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-summary {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.article-author {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.article-time {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.article-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.article-tag {
  padding: 6rpx 12rpx;
  background: rgba(74, 144, 226, 0.1);
  color: var(--primary-color);
  border-radius: var(--rounded-sm);
  font-size: 20rpx;
  font-weight: 500;
}

.article-tag.age-tag {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

/* 加载状态 */
.loading-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  color: var(--text-tertiary);
  font-size: 26rpx;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid var(--border-light);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  color: var(--text-disabled);
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: var(--text-tertiary);
}