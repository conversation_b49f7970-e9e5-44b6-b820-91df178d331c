// index.ts
// 获取应用实例
const app = getApp<IAppOption>()

Component({
  data: {
    // 热门知识数据
    hotKnowledge: [
      {
        id: 1,
        title: '新生儿护理基础知识',
        summary: '从喂养到睡眠，全面了解新生儿护理要点',
        category: '0-1岁',
        publishTime: '2天前',
        thumbnail: '/images/knowledge-1.svg'
      },
      {
        id: 2,
        title: '幼儿早教启蒙指南',
        summary: '科学的早教方法，促进宝宝智力发育',
        category: '1-3岁',
        publishTime: '3天前',
        thumbnail: '/images/knowledge-2.svg'
      },
      {
        id: 3,
        title: '疫苗接种时间表',
        summary: '详细的疫苗接种计划，保护宝宝健康成长',
        category: '健康',
        publishTime: '5天前',
        thumbnail: '/images/knowledge-3.svg'
      }
    ],
    // 政策资讯数据
    policyNews: [
      {
        id: 1,
        title: '2024年育儿补贴新政策发布',
        summary: '多地提高育儿补贴标准，最高可达每月2000元',
        publishTime: '1天前',
        region: '全国',
        isNew: true
      },
      {
        id: 2,
        title: '托育服务补贴申请指南',
        summary: '详解托育补贴申请流程和所需材料',
        publishTime: '3天前',
        region: '北京',
        isNew: false
      },
      {
        id: 3,
        title: '生育津贴计算方式调整',
        summary: '新的计算标准将于下月开始执行',
        publishTime: '1周前',
        region: '上海',
        isNew: false
      }
    ]
  },

  methods: {
    // 跳转到补贴计算页面
    goToCalculator() {
      wx.navigateTo({
        url: '/pages/calculator/calculator'
      })
    },

    // 跳转到育儿知识页面
    goToKnowledge() {
      wx.navigateTo({
        url: '/pages/knowledge/knowledge'
      })
    },

    // 跳转到知识详情页面
    goToKnowledgeDetail(e: any) {
      const id = e.currentTarget.dataset.id
      wx.navigateTo({
        url: `/pages/knowledge-detail/knowledge-detail?id=${id}`
      })
    },

    // 跳转到政策资讯页面
    goToPolicyNews() {
      wx.navigateTo({
        url: '/pages/policy-news/policy-news'
      })
    },

    // 跳转到资讯详情页面
    goToNewsDetail(e: any) {
      const id = e.currentTarget.dataset.id
      wx.navigateTo({
        url: `/pages/news-detail/news-detail?id=${id}`
      })
    },

    // 图片加载失败处理
    onImageError(e: any) {
      console.log('图片加载失败:', e.detail.errMsg)
      // 可以设置默认图片
      const target = e.target
      target.src = '/images/placeholder.svg'
    }
  },

  // 页面生命周期
  lifetimes: {
    attached() {
      // 页面加载时的逻辑
      console.log('首页加载完成')
    }
  }
})
