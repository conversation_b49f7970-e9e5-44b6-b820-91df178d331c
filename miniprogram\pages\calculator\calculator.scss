/**calculator.scss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
}

/* 计算器表单 */
.calculator-form {
  margin-top: 30rpx;
}

.form-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  width: 160rpx;
  flex-shrink: 0;
}

.picker-value {
  flex: 1;
  padding: 20rpx 24rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
}

.picker-value.small {
  padding: 16rpx 20rpx;
  font-size: 26rpx;
}

.picker-value:empty::before {
  content: attr(placeholder);
  color: #999;
}

.input {
  flex: 1;
  padding: 20rpx 24rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
}

.input:focus {
  border-color: #4A90E2;
  background: white;
}

.unit {
  font-size: 26rpx;
  color: #666;
  margin-left: 16rpx;
}

/* 年龄输入区域 */
.age-inputs {
  flex: 1;
}

.age-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.age-item:last-child {
  margin-bottom: 0;
}

.age-label {
  font-size: 24rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

/* 计算按钮 */
.calculate-btn {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  text-align: center;
  padding: 32rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin: 40rpx 0;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
}

/* 计算结果 */
.result-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx 0 40rpx 0;
}

.result-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.result-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.result-list {
  margin-bottom: 40rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-info {
  flex: 1;
}

.subsidy-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.subsidy-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.result-amount {
  text-align: right;
}

.amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #4A90E2;
}

.total-amount {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  padding: 30rpx;
  border-radius: 16rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.total-label {
  font-size: 26rpx;
  display: block;
  margin-bottom: 12rpx;
  opacity: 0.9;
}

.total-value {
  font-size: 48rpx;
  font-weight: bold;
  margin-right: 8rpx;
}

.total-unit {
  font-size: 28rpx;
}

/* 申请指南 */
.apply-guide {
  margin-bottom: 40rpx;
}

.guide-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.guide-steps {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: #4A90E2;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.step-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
}

.btn-secondary,
.btn-primary {
  flex: 1;
  padding: 28rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
}

.btn-primary {
  background: #4A90E2;
  color: white;
}
