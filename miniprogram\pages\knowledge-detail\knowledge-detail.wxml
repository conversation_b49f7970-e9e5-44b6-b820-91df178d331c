<!--knowledge-detail.wxml-->
<page-meta>
  <navigation-bar title="知识详情" back="{{true}}" color="white" background="#4A90E2"></navigation-bar>
</page-meta>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 文章头部 -->
    <view class="article-header">
      <image class="article-banner" src="{{article.banner}}" mode="aspectFill"></image>
      <view class="header-content">
        <text class="article-title">{{article.title}}</text>
        <view class="article-meta">
          <view class="meta-left">
            <image class="author-avatar" src="{{article.authorAvatar}}" mode="aspectFill"></image>
            <view class="author-info">
              <text class="author-name">{{article.author}}</text>
              <text class="publish-time">{{article.publishTime}}</text>
            </view>
          </view>
          <view class="meta-right">
            <view class="stat-item">
              <image class="stat-icon" src="/images/view-icon.png" mode="aspectFit"></image>
              <text class="stat-text">{{article.viewCount}}</text>
            </view>
            <view class="stat-item">
              <image class="stat-icon" src="/images/like-icon.png" mode="aspectFit"></image>
              <text class="stat-text">{{article.likeCount}}</text>
            </view>
          </view>
        </view>
        <view class="article-tags">
          <text class="tag category-tag">{{article.category}}</text>
          <text class="tag age-tag" wx:if="{{article.ageGroup}}">{{article.ageGroup}}</text>
        </view>
      </view>
    </view>
    
    <!-- 文章内容 -->
    <view class="article-content">
      <rich-text nodes="{{article.content}}"></rich-text>
    </view>
    
    <!-- 相关推荐 -->
    <view class="related-section" wx:if="{{relatedArticles.length > 0}}">
      <view class="section-title">相关推荐</view>
      <view class="related-list">
        <view class="related-item" 
              wx:for="{{relatedArticles}}" 
              wx:key="id" 
              bindtap="goToRelated" 
              data-id="{{item.id}}">
          <image class="related-thumb" src="{{item.thumbnail}}" mode="aspectFill"></image>
          <view class="related-content">
            <text class="related-title">{{item.title}}</text>
            <text class="related-summary">{{item.summary}}</text>
            <view class="related-meta">
              <text class="related-author">{{item.author}}</text>
              <text class="related-time">{{item.publishTime}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>

<!-- 底部操作栏 -->
<view class="bottom-actions">
  <view class="action-item" bindtap="toggleLike">
    <image class="action-icon {{isLiked ? 'liked' : ''}}" src="/images/{{isLiked ? 'liked' : 'like'}}-icon.png" mode="aspectFit"></image>
    <text class="action-text">{{isLiked ? '已赞' : '点赞'}}</text>
  </view>
  <view class="action-item" bindtap="toggleFavorite">
    <image class="action-icon {{isFavorited ? 'favorited' : ''}}" src="/images/{{isFavorited ? 'favorited' : 'favorite'}}-icon.png" mode="aspectFit"></image>
    <text class="action-text">{{isFavorited ? '已收藏' : '收藏'}}</text>
  </view>
  <view class="action-item" bindtap="shareArticle">
    <image class="action-icon" src="/images/share-icon.png" mode="aspectFit"></image>
    <text class="action-text">分享</text>
  </view>
  <view class="action-item comment-btn" bindtap="showComments">
    <image class="action-icon" src="/images/comment-icon.png" mode="aspectFit"></image>
    <text class="action-text">评论</text>
  </view>
</view>
