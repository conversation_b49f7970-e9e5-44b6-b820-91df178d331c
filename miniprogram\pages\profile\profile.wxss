/**profile.wxss**/
@import '../../../styles/design-system.scss';

page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
}

/* 用户信息卡片 */
.user-card {
  background: var(--bg-primary);
  border-radius: var(--rounded-lg);
  margin: 30rpx 0;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.user-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: var(--primary-gradient);
  color: white;
  position: relative;
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  background: var(--bg-tertiary);
}

.avatar-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: var(--warning-color);
  color: white;
  font-size: 18rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  border: 2rpx solid white;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
  line-height: 1.4;
}

.login-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  backdrop-filter: blur(10rpx);
}

/* 统计信息 */
.user-stats {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: var(--bg-primary);
}

.stat-item {
  flex: 1;
  text-align: center;
  transition: transform 0.2s ease;
}

.stat-item:active {
  transform: scale(0.95);
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--primary-color);
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  display: block;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background: var(--border-light);
  margin: 0 20rpx;
}

/* 功能菜单 */
.menu-section {
  margin: 20rpx 0;
}

.menu-group {
  background: var(--bg-primary);
  border-radius: var(--rounded-lg);
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-light);
  transition: background-color 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: var(--bg-tertiary);
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--rounded-base);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
  background: var(--bg-tertiary);
}

.calculation-icon { background: rgba(74, 144, 226, 0.1); }
.favorite-icon { background: rgba(244, 67, 54, 0.1); }
.reading-icon { background: rgba(76, 175, 80, 0.1); }
.settings-icon { background: rgba(158, 158, 158, 0.1); }
.feedback-icon { background: rgba(255, 152, 0, 0.1); }
.about-icon { background: rgba(33, 150, 243, 0.1); }

.menu-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: 6rpx;
  flex: 1;
}

.menu-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  display: block;
  flex: 1;
}

.menu-arrow {
  font-size: 28rpx;
  color: var(--text-tertiary);
  margin-left: 16rpx;
}

/* 快捷操作 */
.quick-actions {
  margin: 20rpx 0;
}

.action-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  background: var(--bg-primary);
  border: none;
  border-radius: var(--rounded-base);
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--shadow-light);
  transition: all 0.2s ease;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.action-btn:active {
  transform: scale(0.95);
  background: var(--bg-tertiary);
}

.action-icon {
  font-size: 36rpx;
  margin-bottom: 12rpx;
}

/* 退出登录 */
.logout-section {
  margin: 40rpx 0;
  padding: 0 10rpx;
}

.logout-btn {
  width: 100%;
  background: transparent;
  color: var(--error-color);
  border: 2rpx solid var(--error-color);
  border-radius: var(--rounded-base);
  padding: 28rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.logout-btn:active {
  background: var(--error-color);
  color: white;
}